{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../../../src/code_assist/server.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAiBH,OAAO,KAAK,QAAQ,MAAM,UAAU,CAAC;AAErC,OAAO,EAEL,wBAAwB,EACxB,2BAA2B,EAC3B,mBAAmB,EACnB,sBAAsB,GAEvB,MAAM,gBAAgB,CAAC;AASxB,8DAA8D;AAC9D,MAAM,CAAC,MAAM,oBAAoB,GAC/B,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,qCAAqC,CAAC;AAC5E,MAAM,CAAC,MAAM,uBAAuB,GAAG,YAAY,CAAC;AAEpD,MAAM,OAAO,gBAAgB;IAEhB;IACA;IACA;IAHX,YACW,IAAgB,EAChB,SAAkB,EAClB,cAA2B,EAAE;QAF7B,SAAI,GAAJ,IAAI,CAAY;QAChB,cAAS,GAAT,SAAS,CAAS;QAClB,gBAAW,GAAX,WAAW,CAAkB;IACrC,CAAC;IAEJ,KAAK,CAAC,qBAAqB,CACzB,GAA8B;QAE9B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CACrC,uBAAuB,EACvB,wBAAwB,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAC7C,GAAG,CAAC,MAAM,EAAE,WAAW,CACxB,CAAC;QACF,OAAO,CAAC,KAAK,SAAS,CAAC;YACrB,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC/B,MAAM,2BAA2B,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;IACP,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,GAA8B;QAE9B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAClC,iBAAiB,EACjB,wBAAwB,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAC7C,GAAG,CAAC,MAAM,EAAE,WAAW,CACxB,CAAC;QACF,OAAO,2BAA2B,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,WAAW,CACf,GAAuB;QAEvB,OAAO,MAAM,IAAI,CAAC,YAAY,CAC5B,aAAa,EACb,GAAG,CACJ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,GAA0B;QAE1B,OAAO,MAAM,IAAI,CAAC,YAAY,CAC5B,gBAAgB,EAChB,GAAG,CACJ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,GAA0B;QAC1C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAClC,aAAa,EACb,mBAAmB,CAAC,GAAG,CAAC,CACzB,CAAC;QACF,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,IAA4B;QAE5B,MAAM,KAAK,EAAE,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,MAAc,EACd,GAAW,EACX,MAAoB;QAEpB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;YAClC,GAAG,EAAE,GAAG,oBAAoB,IAAI,uBAAuB,IAAI,MAAM,EAAE;YACnE,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;aAC5B;YACD,YAAY,EAAE,MAAM;YACpB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACzB,MAAM;SACP,CAAC,CAAC;QACH,OAAO,GAAG,CAAC,IAAS,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAAc,EACd,GAAW,EACX,MAAoB;QAEpB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;YAClC,GAAG,EAAE,GAAG,oBAAoB,IAAI,uBAAuB,IAAI,MAAM,EAAE;YACnE,MAAM,EAAE,MAAM;YACd,MAAM,EAAE;gBACN,GAAG,EAAE,KAAK;aACX;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;aAC5B;YACD,YAAY,EAAE,QAAQ;YACtB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACzB,MAAM;SACP,CAAC,CAAC;QAEH,OAAO,CAAC,KAAK,SAAS,CAAC;YACrB,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;gBAClC,KAAK,EAAE,GAAG,CAAC,IAAmB;gBAC9B,SAAS,EAAE,QAAQ,EAAE,4CAA4C;aAClE,CAAC,CAAC;YAEH,IAAI,aAAa,GAAa,EAAE,CAAC;YACjC,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,EAAE,EAAE,CAAC;gBAC5B,8DAA8D;gBAC9D,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;oBAChB,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC/B,SAAS,CAAC,mBAAmB;oBAC/B,CAAC;oBACD,MAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAM,CAAC;oBAChD,aAAa,GAAG,EAAE,CAAC,CAAC,kCAAkC;gBACxD,CAAC;qBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC3C,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,uCAAuC,IAAI,EAAE,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;IACP,CAAC;CACF"}