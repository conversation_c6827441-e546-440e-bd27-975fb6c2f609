{"version": 3, "file": "bfsFileSearch.test.js", "sourceRoot": "", "sources": ["../../../src/utils/bfsFileSearch.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAC9D,OAAO,KAAK,UAAU,MAAM,aAAa,CAAC;AAC1C,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAC;AAC1C,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAE3E,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACd,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACvB,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAEzB,MAAM,gBAAgB,GAAG,CAAC,IAAY,EAAE,MAAe,EAAa,EAAE;IACpE,MAAM,MAAM,GAAG,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC;IAC/B,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC;IAC7B,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC;IACnC,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAQF,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,UAAU,CAAC,GAAG,EAAE;QACd,EAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,MAAM,CAAC,OAA0C,CAAC;QACtE,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,iBAAiB,CAAC;YACvC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC;YACnC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC;SACpC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC;QACvE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,MAAM,CAAC,OAA0C,CAAC;QACtE,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACtD,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;gBACpB,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;YAC7C,CAAC;YACD,IAAI,GAAG,KAAK,cAAc,EAAE,CAAC;gBAC3B,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC;QACvE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;QACnD,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,MAAM,CAAC,OAA0C,CAAC;QACtE,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACtD,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;gBACpB,OAAO;oBACL,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC;oBAClC,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC;iBACnC,CAAC;YACJ,CAAC;YACD,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;gBAC5B,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC;YACD,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;gBAC5B,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE;YAC1C,QAAQ,EAAE,WAAW;YACrB,UAAU,EAAE,CAAC,SAAS,CAAC;SACxB,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;QAC5C,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,MAAM,CAAC,OAA0C,CAAC;QACtE,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACtD,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;gBACpB,OAAO;oBACL,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC;oBAClC,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC;iBACnC,CAAC;YACJ,CAAC;YACD,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;gBAC5B,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC;YACD,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;gBAC5B,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE;YAC1C,QAAQ,EAAE,WAAW;YACrB,OAAO,EAAE,CAAC;SACX,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;QAC/C,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,YAAY,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,YAAY,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,MAAM,CAAC,OAA0C,CAAC;QACtE,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACtD,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;gBACpB,OAAO;oBACL,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC;oBACpC,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC;oBAClC,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC;iBACnC,CAAC;YACJ,CAAC;YACD,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;gBAC5B,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC;YACD,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;gBAC5B,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAEtD,MAAM,WAAW,GAAG,IAAI,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACtD,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE;YAC1C,QAAQ,EAAE,WAAW;YACrB,WAAW;SACZ,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}