/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
export interface ClientMetadata {
    ideType?: ClientMetadataIdeType;
    ideVersion?: string;
    pluginVersion?: string;
    platform?: ClientMetadataPlatform;
    updateChannel?: string;
    duetProject?: string;
    pluginType?: ClientMetadataPluginType;
    ideName?: string;
}
export type ClientMetadataIdeType = 'IDE_UNSPECIFIED' | 'VSCODE' | 'INTELLIJ' | 'VSCODE_CLOUD_WORKSTATION' | 'INTELLIJ_CLOUD_WORKSTATION' | 'CLOUD_SHELL';
export type ClientMetadataPlatform = 'PLATFORM_UNSPECIFIED' | 'DARWIN_AMD64' | 'DARWIN_ARM64' | 'LINUX_AMD64' | 'LINUX_ARM64' | 'WINDOWS_AMD64';
export type ClientMetadataPluginType = 'PLUGIN_UNSPECIFIED' | 'CLOUD_CODE' | 'GEMINI' | 'AIPLUGIN_INTELLIJ' | 'AIPLUGIN_STUDIO';
export interface LoadCodeAssistRequest {
    cloudaicompanionProject?: string;
    metadata: ClientMetadata;
}
/**
 * Represents LoadCodeAssistResponse proto json field
 * http://google3/google/internal/cloud/code/v1internal/cloudcode.proto;l=224
 */
export interface LoadCodeAssistResponse {
    currentTier?: GeminiUserTier | null;
    allowedTiers?: GeminiUserTier[] | null;
    ineligibleTiers?: IneligibleTier[] | null;
    cloudaicompanionProject?: string | null;
}
/**
 * GeminiUserTier reflects the structure received from the CodeAssist when calling LoadCodeAssist.
 */
export interface GeminiUserTier {
    id: UserTierId;
    name: string;
    description: string;
    userDefinedCloudaicompanionProject?: boolean | null;
    isDefault?: boolean;
    privacyNotice?: PrivacyNotice;
    hasAcceptedTos?: boolean;
    hasOnboardedPreviously?: boolean;
}
/**
 * Includes information specifying the reasons for a user's ineligibility for a specific tier.
 * @param reasonCode mnemonic code representing the reason for in-eligibility.
 * @param reasonMessage message to display to the user.
 * @param tierId id of the tier.
 * @param tierName name of the tier.
 */
export interface IneligibleTier {
    reasonCode: IneligibleTierReasonCode;
    reasonMessage: string;
    tierId: UserTierId;
    tierName: string;
}
/**
 * List of predefined reason codes when a tier is blocked from a specific tier.
 * https://source.corp.google.com/piper///depot/google3/google/internal/cloud/code/v1internal/cloudcode.proto;l=378
 */
export declare enum IneligibleTierReasonCode {
    DASHER_USER = "DASHER_USER",
    INELIGIBLE_ACCOUNT = "INELIGIBLE_ACCOUNT",
    NON_USER_ACCOUNT = "NON_USER_ACCOUNT",
    RESTRICTED_AGE = "RESTRICTED_AGE",
    RESTRICTED_NETWORK = "RESTRICTED_NETWORK",
    UNKNOWN = "UNKNOWN",
    UNKNOWN_LOCATION = "UNKNOWN_LOCATION",
    UNSUPPORTED_LOCATION = "UNSUPPORTED_LOCATION"
}
/**
 * UserTierId represents IDs returned from the Cloud Code Private API representing a user's tier
 *
 * //depot/google3/cloud/developer_experience/cloudcode/pa/service/usertier.go;l=16
 */
export declare enum UserTierId {
    FREE = "free-tier",
    LEGACY = "legacy-tier",
    STANDARD = "standard-tier"
}
/**
 * PrivacyNotice reflects the structure received from the CodeAssist in regards to a tier
 * privacy notice.
 */
export interface PrivacyNotice {
    showNotice: boolean;
    noticeText?: string;
}
/**
 * Proto signature of OnboardUserRequest as payload to OnboardUser call
 */
export interface OnboardUserRequest {
    tierId: string | undefined;
    cloudaicompanionProject: string | undefined;
    metadata: ClientMetadata | undefined;
}
/**
 * Represents LongrunningOperation proto
 * http://google3/google/longrunning/operations.proto;rcl=698857719;l=107
 */
export interface LongrunningOperationResponse {
    name: string;
    done?: boolean;
    response?: OnboardUserResponse;
}
/**
 * Represents OnboardUserResponse proto
 * http://google3/google/internal/cloud/code/v1internal/cloudcode.proto;l=215
 */
export interface OnboardUserResponse {
    cloudaicompanionProject?: {
        id: string;
        name: string;
    };
}
/**
 * Status code of user license status
 * it does not stricly correspond to the proto
 * Error value is an additional value assigned to error responses from OnboardUser
 */
export declare enum OnboardUserStatusCode {
    Default = "DEFAULT",
    Notice = "NOTICE",
    Warning = "WARNING",
    Error = "ERROR"
}
/**
 * Status of user onboarded to gemini
 */
export interface OnboardUserStatus {
    statusCode: OnboardUserStatusCode;
    displayMessage: string;
    helpLink: HelpLinkUrl | undefined;
}
export interface HelpLinkUrl {
    description: string;
    url: string;
}
