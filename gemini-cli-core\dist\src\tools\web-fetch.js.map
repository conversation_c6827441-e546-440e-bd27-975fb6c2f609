{"version": 3, "file": "web-fetch.js", "sourceRoot": "", "sources": ["../../../src/tools/web-fetch.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EACL,QAAQ,EAGR,uBAAuB,GACxB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAU,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,8CAA8C,CAAC;AAC/E,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAClE,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAEvC,MAAM,oBAAoB,GAAG,KAAK,CAAC;AACnC,MAAM,kBAAkB,GAAG,MAAM,CAAC;AAElC,gDAAgD;AAChD,SAAS,WAAW,CAAC,IAAY;IAC/B,MAAM,QAAQ,GAAG,sBAAsB,CAAC;IACxC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AACpC,CAAC;AAiCD;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,QAAwC;IAG3C;IAF7B,MAAM,CAAU,IAAI,GAAW,WAAW,CAAC;IAE3C,YAA6B,MAAc;QACzC,KAAK,CACH,YAAY,CAAC,IAAI,EACjB,UAAU,EACV,2OAA2O,EAC3O;YACE,UAAU,EAAE;gBACV,MAAM,EAAE;oBACN,WAAW,EACT,sSAAsS;oBACxS,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACpB,IAAI,EAAE,QAAQ;SACf,CACF,CAAC;QAhByB,WAAM,GAAN,MAAM,CAAQ;IAiB3C,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,MAA0B,EAC1B,MAAmB;QAEnB,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO;gBACL,UAAU,EAAE,iDAAiD;gBAC7D,aAAa,EAAE,iDAAiD;aACjE,CAAC;QACJ,CAAC;QACD,gDAAgD;QAChD,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAElB,qCAAqC;QACrC,IAAI,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzD,GAAG,GAAG,GAAG;iBACN,OAAO,CAAC,YAAY,EAAE,2BAA2B,CAAC;iBAClD,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,gBAAgB,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;YACnE,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CACb,mCAAmC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAC5E,CAAC;YACJ,CAAC;YACD,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnC,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,EAAE;gBAChC,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE;oBACT,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE;oBAChD,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;iBACpC;aACF,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;YAEpC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;YACnD,MAAM,cAAc,GAAG,sCAAsC,MAAM,CAAC,MAAM;;;;;EAK9E,WAAW;IACT,CAAC;YACC,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,eAAe,CAC/C,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,EACrD,EAAE,EACF,MAAM,CACP,CAAC;YACF,MAAM,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACjD,OAAO;gBACL,UAAU,EAAE,UAAU;gBACtB,aAAa,EAAE,eAAe,GAAG,kCAAkC;aACpE,CAAC;QACJ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,KAAK,GAAG,CAAU,CAAC;YACzB,MAAM,YAAY,GAAG,mCAAmC,GAAG,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;YAChF,OAAO;gBACL,UAAU,EAAE,UAAU,YAAY,EAAE;gBACpC,aAAa,EAAE,UAAU,YAAY,EAAE;aACxC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,cAAc,CAAC,MAA0B;QACvC,IACE,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,CAAC,eAAe,CAAC,QAAQ,CACvB,IAAI,CAAC,MAAM,CAAC,UAAqC,EACjD,MAAM,CACP,EACD,CAAC;YACD,OAAO,sCAAsC,CAAC;QAChD,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAClD,OAAO,kFAAkF,CAAC;QAC5F,CAAC;QACD,IACE,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAClC,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,EACnC,CAAC;YACD,OAAO,uFAAuF,CAAC;QACjG,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,MAA0B;QACvC,MAAM,aAAa,GACjB,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG;YACxB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YACxC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;QACpB,OAAO,kDAAkD,aAAa,GAAG,CAAC;IAC5E,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,MAA0B;QAE1B,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,4EAA4E;QAC5E,wCAAwC;QACxC,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YAClD,IAAI,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzD,OAAO,GAAG;qBACP,OAAO,CAAC,YAAY,EAAE,2BAA2B,CAAC;qBAClD,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YAC5B,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC,CAAC,CAAC;QAEH,MAAM,mBAAmB,GAAgC;YACvD,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,mBAAmB;YAC1B,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,IAAI;YACJ,SAAS,EAAE,KAAK,EAAE,OAAgC,EAAE,EAAE;gBACpD,IAAI,OAAO,KAAK,uBAAuB,CAAC,aAAa,EAAE,CAAC;oBACtD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;SACF,CAAC;QACF,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAA0B,EAC1B,MAAmB;QAEnB,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE,+CAA+C,eAAe,EAAE;gBAC5E,aAAa,EAAE,eAAe;aAC/B,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;QACjC,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QAEnC,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;QAEnD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,eAAe,CACjD,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EACjD,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,EAAE,EAC/B,MAAM,CACP,CAAC;YAEF,OAAO,CAAC,KAAK,CACX,4CAA4C,UAAU,CAAC,SAAS,CAC9D,CAAC,EACD,EAAE,CACH,OAAO,EACR,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAClC,CAAC;YAEF,IAAI,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnD,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC;YACpE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,iBAEvC,CAAC;YACd,MAAM,OAAO,GAAG,iBAAiB,EAAE,eAEtB,CAAC;YACd,MAAM,iBAAiB,GAAG,iBAAiB,EAAE,iBAEhC,CAAC;YAEd,iBAAiB;YACjB,IAAI,eAAe,GAAG,KAAK,CAAC;YAE5B,IACE,cAAc,EAAE,WAAW;gBAC3B,cAAc,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EACrC,CAAC;gBACD,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC,GAAG,CAChD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAC5B,CAAC;gBACF,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,8BAA8B,CAAC,EAAE,CAAC;oBACnE,eAAe,GAAG,IAAI,CAAC;gBACzB,CAAC;YACH,CAAC;iBAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;gBACpD,yCAAyC;gBACzC,eAAe,GAAG,IAAI,CAAC;YACzB,CAAC;YAED,IACE,CAAC,eAAe;gBAChB,CAAC,YAAY,CAAC,IAAI,EAAE;gBACpB,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,EAClC,CAAC;gBACD,oHAAoH;gBACpH,eAAe,GAAG,IAAI,CAAC;YACzB,CAAC;YAED,IAAI,eAAe,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,mBAAmB,GAAa,EAAE,CAAC;YACzC,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,CAAC,OAAO,CAAC,CAAC,MAA0B,EAAE,KAAa,EAAE,EAAE;oBAC5D,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,EAAE,KAAK,IAAI,UAAU,CAAC;oBAC9C,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,aAAa,CAAC,CAAC,6BAA6B;oBAC3E,mBAAmB,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC;gBAC/D,CAAC,CAAC,CAAC;gBAEH,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtD,MAAM,UAAU,GAA6C,EAAE,CAAC;oBAChE,iBAAiB,CAAC,OAAO,CAAC,CAAC,OAA6B,EAAE,EAAE;wBAC1D,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;4BACrD,MAAM,cAAc,GAAG,OAAO,CAAC,qBAAqB;iCACjD,GAAG,CAAC,CAAC,UAAkB,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC,GAAG,CAAC;iCAClD,IAAI,CAAC,EAAE,CAAC,CAAC;4BACZ,UAAU,CAAC,IAAI,CAAC;gCACd,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;gCAC/B,MAAM,EAAE,cAAc;6BACvB,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC,CAAC,CAAC;oBAEH,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;oBAC7C,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBAC7C,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;wBAC/B,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;oBAC7D,CAAC,CAAC,CAAC;oBACH,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACxC,CAAC;gBAED,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACnC,YAAY,IAAI;;;EAGxB,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3B,CAAC;YACH,CAAC;YAED,MAAM,UAAU,GAAG,YAAY,CAAC;YAEhC,OAAO,CAAC,KAAK,CACX,sDAAsD,UAAU,SAAS,EACzE,UAAU,CACX,CAAC;YAEF,OAAO;gBACL,UAAU;gBACV,aAAa,EAAE,gCAAgC;aAChD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,4CAA4C,UAAU,CAAC,SAAS,CACnF,CAAC,EACD,EAAE,CACH,SAAS,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YACnC,OAAO;gBACL,UAAU,EAAE,UAAU,YAAY,EAAE;gBACpC,aAAa,EAAE,UAAU,YAAY,EAAE;aACxC,CAAC;QACJ,CAAC;IACH,CAAC"}