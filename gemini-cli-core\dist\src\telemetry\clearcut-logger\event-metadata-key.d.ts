/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
export declare enum EventMetadataKey {
    GEMINI_CLI_KEY_UNKNOWN = 0,
    GEMINI_CLI_START_SESSION_MODEL = 1,
    GEMINI_CLI_START_SESSION_EMBEDDING_MODEL = 2,
    GEMINI_CLI_START_SESSION_SANDBOX = 3,
    GEMINI_CLI_START_SESSION_CORE_TOOLS = 4,
    GEMINI_CLI_START_SESSION_APPROVAL_MODE = 5,
    GEMINI_CLI_START_SESSION_API_KEY_ENABLED = 6,
    GEMINI_CLI_START_SESSION_VERTEX_API_ENABLED = 7,
    GEMINI_CLI_START_SESSION_DEBUG_MODE_ENABLED = 8,
    GEMINI_CLI_START_SESSION_MCP_SERVERS = 9,
    GEMINI_CLI_START_SESSION_TELEMETRY_ENABLED = 10,
    GEMINI_CLI_START_SESSION_TELEMETRY_LOG_USER_PROMPTS_ENABLED = 11,
    GEMINI_CLI_START_SESSION_RESPECT_GITIGNORE = 12,
    GEMINI_CLI_USER_PROMPT_LENGTH = 13,
    GEMINI_CLI_TOOL_CALL_NAME = 14,
    GEMINI_CLI_TOOL_CALL_DECISION = 15,
    GEMINI_CLI_TOOL_CALL_SUCCESS = 16,
    GEMINI_CLI_TOOL_CALL_DURATION_MS = 17,
    GEMINI_CLI_TOOL_ERROR_MESSAGE = 18,
    GEMINI_CLI_TOOL_CALL_ERROR_TYPE = 19,
    GEMINI_CLI_API_REQUEST_MODEL = 20,
    GEMINI_CLI_API_RESPONSE_MODEL = 21,
    GEMINI_CLI_API_RESPONSE_STATUS_CODE = 22,
    GEMINI_CLI_API_RESPONSE_DURATION_MS = 23,
    GEMINI_CLI_API_ERROR_MESSAGE = 24,
    GEMINI_CLI_API_RESPONSE_INPUT_TOKEN_COUNT = 25,
    GEMINI_CLI_API_RESPONSE_OUTPUT_TOKEN_COUNT = 26,
    GEMINI_CLI_API_RESPONSE_CACHED_TOKEN_COUNT = 27,
    GEMINI_CLI_API_RESPONSE_THINKING_TOKEN_COUNT = 28,
    GEMINI_CLI_API_RESPONSE_TOOL_TOKEN_COUNT = 29,
    GEMINI_CLI_API_ERROR_MODEL = 30,
    GEMINI_CLI_API_ERROR_TYPE = 31,
    GEMINI_CLI_API_ERROR_STATUS_CODE = 32,
    GEMINI_CLI_API_ERROR_DURATION_MS = 33,
    GEMINI_CLI_END_SESSION_ID = 34
}
export declare function getEventMetadataKey(keyName: string): EventMetadataKey | undefined;
