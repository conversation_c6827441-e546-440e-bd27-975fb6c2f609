{"version": 3, "file": "clearcut-logger.js", "sourceRoot": "", "sources": ["../../../../src/telemetry/clearcut-logger/clearcut-logger.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAChC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAEL,eAAe,GAMhB,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAE3D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;AAE7D,MAAM,wBAAwB,GAAG,eAAe,CAAC;AACjD,MAAM,qBAAqB,GAAG,YAAY,CAAC;AAC3C,MAAM,oBAAoB,GAAG,WAAW,CAAC;AACzC,MAAM,sBAAsB,GAAG,aAAa,CAAC;AAC7C,MAAM,uBAAuB,GAAG,cAAc,CAAC;AAC/C,MAAM,oBAAoB,GAAG,WAAW,CAAC;AACzC,MAAM,sBAAsB,GAAG,aAAa,CAAC;AAM7C,wGAAwG;AACxG,sGAAsG;AACtG,MAAM,OAAO,cAAc;IACjB,MAAM,CAAC,QAAQ,CAAiB;IAChC,MAAM,CAAU;IACxB,+FAA+F;IAC9E,MAAM,GAAQ,EAAE,CAAC;IAC1B,eAAe,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC;IACrC,iBAAiB,GAAW,IAAI,GAAG,EAAE,CAAC,CAAC,iDAAiD;IAEhG,YAAoB,MAAe;QACjC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,MAAe;QAChC,IAAI,MAAM,KAAK,SAAS,IAAI,CAAC,MAAM,EAAE,yBAAyB,EAAE;YAC9D,OAAO,SAAS,CAAC;QACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED,+FAA+F;IAC/F,eAAe,CAAC,KAAU;QACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf;gBACE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE;gBACzB,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;aAC7C;SACF,CAAC,CAAC;IACL,CAAC;IAED,cAAc,CAAC,IAAY,EAAE,IAAY;QACvC,OAAO;YACL,YAAY,EAAE,YAAY;YAC1B,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,IAAI;YAChB,iBAAiB,EAAE,mBAAmB,EAAE;YACxC,cAAc,EAAE,CAAC,IAAI,CAAa;SACnC,CAAC;IACJ,CAAC;IAED,aAAa;QACX,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,eAAe;QACb,IAAI,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAClD,CAAC;QACD,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAEvB,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,MAAM,OAAO,GAAG;gBACd;oBACE,eAAe,EAAE,SAAS;oBAC1B,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE;oBAC3B,SAAS,EAAE,YAAY;iBACxB;aACF,CAAC;YACF,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,qBAAqB;gBAC/B,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,EAAE,gBAAgB,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;aACvD,CAAC;YACF,MAAM,IAAI,GAAa,EAAE,CAAC;YAC1B,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACzC,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;gBACxC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACjB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBACpB,IAAI,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;oBAChC,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,CAAC,CAAC,CAAC;gBAClD,CAAC;gBACD,+DAA+D;gBAC/D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC;gBACrC,MAAM,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAW,EAAE,EAAE;YACtB,IAAI,CAAC;gBACH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YAC3C,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBACnD,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,+EAA+E;IAC/E,iBAAiB,CAAC,GAAW;QAC3B,oEAAoE;QACpE,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,6EAA6E;QAC7E,8EAA8E;QAC9E,8EAA8E;QAC9E,aAAa;QACb,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,IAAI,IAAI,GAAG,IAAI,CAAC;QAEhB,0EAA0E;QAC1E,8EAA8E;QAC9E,6CAA6C;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,EAAE,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjD,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,uEAAuE;YACvE,wBAAwB;YACxB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,SAAS,GAAG;YAChB,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC;SAC9B,CAAC;QACF,IAAI,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,oBAAoB,CAAC,KAAwB;QAC3C,MAAM,IAAI,GAAG;YACX;gBACE,cAAc,EAAE,gBAAgB,CAAC,8BAA8B;gBAC/D,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,wCAAwC;gBAC3D,KAAK,EAAE,KAAK,CAAC,eAAe;aAC7B;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,gCAAgC;gBACjE,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE;aACxC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,mCAAmC;gBACpE,KAAK,EAAE,KAAK,CAAC,kBAAkB;aAChC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,sCAAsC;gBACvE,KAAK,EAAE,KAAK,CAAC,aAAa;aAC3B;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,wCAAwC;gBAC3D,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE;aACxC;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,2CAA2C;gBAC9D,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,QAAQ,EAAE;aAC1C;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,2CAA2C;gBAC9D,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE;aACtC;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,2CAA2C;gBAC9D,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,QAAQ,EAAE;aAC1C;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,oCAAoC;gBACrE,KAAK,EAAE,KAAK,CAAC,WAAW;aACzB;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,2CAA2C;gBAC9D,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,QAAQ,EAAE;aAC1C;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,0CAA0C;gBAC7D,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,QAAQ,EAAE;aAC1C;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,2DAA2D;gBAC9E,KAAK,EAAE,KAAK,CAAC,kCAAkC,CAAC,QAAQ,EAAE;aAC3D;SACF,CAAC;QACF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1E,gCAAgC;QAChC,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,iBAAiB,CAAC,KAAsB;QACtC,MAAM,IAAI,GAAG;YACX;gBACE,cAAc,EAAE,gBAAgB,CAAC,6BAA6B;gBAC9D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC;aAC3C;SACF,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,gBAAgB,CAAC,KAAoB;QACnC,MAAM,IAAI,GAAG;YACX;gBACE,cAAc,EAAE,gBAAgB,CAAC,yBAAyB;gBAC1D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC;aAC3C;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,6BAA6B;gBAC9D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;aACtC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,4BAA4B;gBAC7D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC;aACrC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,gCAAgC;gBACjE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC;aACzC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,6BAA6B;gBAC9D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;aACnC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,+BAA+B;gBAChE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC;aACxC;SACF,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAAC;QACtE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,kBAAkB,CAAC,KAAsB;QACvC,MAAM,IAAI,GAAG;YACX;gBACE,cAAc,EAAE,gBAAgB,CAAC,4BAA4B;gBAC7D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;aACnC;SACF,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,mBAAmB,CAAC,KAAuB;QACzC,MAAM,IAAI,GAAG;YACX;gBACE,cAAc,EAAE,gBAAgB,CAAC,6BAA6B;gBAC9D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;aACnC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,mCAAmC;gBACpE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC;aACzC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,mCAAmC;gBACpE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC;aACzC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,4BAA4B;gBAC7D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;aACnC;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,yCAAyC;gBAC5D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC;aAC/C;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,0CAA0C;gBAC7D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,kBAAkB,CAAC;aAChD;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,0CAA0C;gBAC7D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,0BAA0B,CAAC;aACxD;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,4CAA4C;gBAC/D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,oBAAoB,CAAC;aAClD;YACD;gBACE,cAAc,EACZ,gBAAgB,CAAC,4CAA4C;gBAC/D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC;aAC9C;SACF,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,gBAAgB,CAAC,KAAoB;QACnC,MAAM,IAAI,GAAG;YACX;gBACE,cAAc,EAAE,gBAAgB,CAAC,0BAA0B;gBAC3D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;aACnC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,yBAAyB;gBAC1D,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC;aACxC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,gCAAgC;gBACjE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC;aACzC;YACD;gBACE,cAAc,EAAE,gBAAgB,CAAC,gCAAgC;gBACjE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC;aACzC;SACF,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAAC;QACtE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,kBAAkB,CAAC,KAAsB;QACvC,MAAM,IAAI,GAAG;YACX;gBACE,cAAc,EAAE,gBAAgB,CAAC,yBAAyB;gBAC1D,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC3C;SACF,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC,CAAC;QACxE,oCAAoC;QACpC,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,QAAQ;QACN,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;CACF"}