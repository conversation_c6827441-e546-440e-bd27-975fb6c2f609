/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { AuthClient } from 'google-auth-library';
import { LoadCodeAssistResponse, LoadCodeAssistRequest, OnboardUserRequest, LongrunningOperationResponse } from './types.js';
import { GenerateContentResponse, GenerateContentParameters, CountTokensParameters, EmbedContentResponse, CountTokensResponse, EmbedContentParameters } from '@google/genai';
import { ContentGenerator } from '../core/contentGenerator.js';
/** HTTP options to be used in each of the requests. */
export interface HttpOptions {
    /** Additional HTTP headers to be sent with the request. */
    headers?: Record<string, string>;
}
export declare const CODE_ASSIST_ENDPOINT: string;
export declare const CODE_ASSIST_API_VERSION = "v1internal";
export declare class CodeAssistServer implements ContentGenerator {
    readonly auth: AuthClient;
    readonly projectId?: string | undefined;
    readonly httpOptions: HttpOptions;
    constructor(auth: AuthClient, projectId?: string | undefined, httpOptions?: HttpOptions);
    generateContentStream(req: GenerateContentParameters): Promise<AsyncGenerator<GenerateContentResponse>>;
    generateContent(req: GenerateContentParameters): Promise<GenerateContentResponse>;
    onboardUser(req: OnboardUserRequest): Promise<LongrunningOperationResponse>;
    loadCodeAssist(req: LoadCodeAssistRequest): Promise<LoadCodeAssistResponse>;
    countTokens(req: CountTokensParameters): Promise<CountTokensResponse>;
    embedContent(_req: EmbedContentParameters): Promise<EmbedContentResponse>;
    callEndpoint<T>(method: string, req: object, signal?: AbortSignal): Promise<T>;
    streamEndpoint<T>(method: string, req: object, signal?: AbortSignal): Promise<AsyncGenerator<T>>;
}
