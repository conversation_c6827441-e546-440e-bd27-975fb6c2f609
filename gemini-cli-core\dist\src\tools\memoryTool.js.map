{"version": 3, "file": "memoryTool.js", "sourceRoot": "", "sources": ["../../../src/tools/memoryTool.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAc,MAAM,YAAY,CAAC;AAClD,OAAO,KAAK,EAAE,MAAM,aAAa,CAAC;AAClC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC;AAE7B,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,aAAa;IACnB,WAAW,EACT,4OAA4O;IAC9O,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,WAAW,EACT,qGAAqG;aACxG;SACF;QACD,QAAQ,EAAE,CAAC,MAAM,CAAC;KACnB;CACF,CAAC;AAEF,MAAM,qBAAqB,GAAG;;;;;;;;;;;;;;;;;CAiB7B,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAG,SAAS,CAAC;AAC3C,MAAM,CAAC,MAAM,wBAAwB,GAAG,WAAW,CAAC;AACpD,MAAM,CAAC,MAAM,qBAAqB,GAAG,0BAA0B,CAAC;AAEhE,yFAAyF;AACzF,wFAAwF;AACxF,IAAI,uBAAuB,GAAsB,wBAAwB,CAAC;AAE1E,MAAM,UAAU,mBAAmB,CAAC,WAA8B;IAChE,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QAC/B,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,uBAAuB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;SAAM,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;QACpD,uBAAuB,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;IAC/C,CAAC;AACH,CAAC;AAED,MAAM,UAAU,0BAA0B;IACxC,IAAI,KAAK,CAAC,OAAO,CAAC,uBAAuB,CAAC,EAAE,CAAC;QAC3C,OAAO,uBAAuB,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,OAAO,uBAAuB,CAAC;AACjC,CAAC;AAED,MAAM,UAAU,uBAAuB;IACrC,IAAI,KAAK,CAAC,OAAO,CAAC,uBAAuB,CAAC,EAAE,CAAC;QAC3C,OAAO,uBAAuB,CAAC;IACjC,CAAC;IACD,OAAO,CAAC,uBAAuB,CAAC,CAAC;AACnC,CAAC;AAMD,SAAS,uBAAuB;IAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,iBAAiB,EAAE,0BAA0B,EAAE,CAAC,CAAC;AAC/E,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAAC,cAAsB;IACrD,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAC3C,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC;QACxE,OAAO,EAAE,CAAC;IACZ,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,OAAO,UAAW,SAAQ,QAAsC;IACpE,MAAM,CAAU,IAAI,GAAW,oBAAoB,CAAC,IAAI,CAAC;IACzD;QACE,KAAK,CACH,UAAU,CAAC,IAAI,EACf,aAAa,EACb,qBAAqB,EACrB,oBAAoB,CAAC,UAAqC,CAC3D,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAChC,IAAY,EACZ,cAAsB,EACtB,SAWC;QAED,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAChC,wFAAwF;QACxF,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9D,MAAM,aAAa,GAAG,KAAK,aAAa,EAAE,CAAC;QAE3C,IAAI,CAAC;YACH,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACzE,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,CAAC;gBACH,OAAO,GAAG,MAAM,SAAS,CAAC,QAAQ,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAC9D,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC;gBACZ,4DAA4D;YAC9D,CAAC;YAED,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YAE3D,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;gBACvB,qDAAqD;gBACrD,MAAM,SAAS,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;gBACnD,OAAO,IAAI,GAAG,SAAS,GAAG,qBAAqB,KAAK,aAAa,IAAI,CAAC;YACxE,CAAC;iBAAM,CAAC;gBACN,0DAA0D;gBAC1D,MAAM,qBAAqB,GACzB,WAAW,GAAG,qBAAqB,CAAC,MAAM,CAAC;gBAC7C,IAAI,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;gBACxE,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC7B,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,cAAc;gBACpD,CAAC;gBAED,MAAM,mBAAmB,GAAG,OAAO;qBAChC,SAAS,CAAC,CAAC,EAAE,qBAAqB,CAAC;qBACnC,OAAO,EAAE,CAAC;gBACb,IAAI,cAAc,GAAG,OAAO;qBACzB,SAAS,CAAC,qBAAqB,EAAE,iBAAiB,CAAC;qBACnD,OAAO,EAAE,CAAC;gBACb,MAAM,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;gBAEhE,cAAc,IAAI,KAAK,aAAa,EAAE,CAAC;gBACvC,OAAO;oBACL,GAAG,mBAAmB,KAAK,cAAc,CAAC,SAAS,EAAE,KAAK,kBAAkB,EAAE,CAAC,OAAO,EAAE;wBACxF,IAAI,CAAC;YACT,CAAC;YACD,MAAM,SAAS,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CACX,6CAA6C,cAAc,GAAG,EAC9D,KAAK,CACN,CAAC;YACF,MAAM,IAAI,KAAK,CACb,4CAA4C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrG,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAAwB,EACxB,OAAoB;QAEpB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,YAAY,GAAG,8CAA8C,CAAC;YACpE,OAAO;gBACL,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;gBACnE,aAAa,EAAE,UAAU,YAAY,EAAE;aACxC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,gDAAgD;YAChD,MAAM,UAAU,CAAC,qBAAqB,CAAC,IAAI,EAAE,uBAAuB,EAAE,EAAE;gBACtE,QAAQ,EAAE,EAAE,CAAC,QAAQ;gBACrB,SAAS,EAAE,EAAE,CAAC,SAAS;gBACvB,KAAK,EAAE,EAAE,CAAC,KAAK;aAChB,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,gCAAgC,IAAI,GAAG,CAAC;YAC/D,OAAO;gBACL,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;gBACtE,aAAa,EAAE,cAAc;aAC9B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAChB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzD,OAAO,CAAC,KAAK,CACX,sDAAsD,IAAI,MAAM,YAAY,EAAE,CAC/E,CAAC;YACF,OAAO;gBACL,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC;oBACzB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,kCAAkC,YAAY,EAAE;iBACxD,CAAC;gBACF,aAAa,EAAE,wBAAwB,YAAY,EAAE;aACtD,CAAC;QACJ,CAAC;IACH,CAAC"}