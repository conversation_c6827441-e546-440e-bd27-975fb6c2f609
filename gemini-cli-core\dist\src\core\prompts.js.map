{"version": 3, "file": "prompts.js", "sourceRoot": "", "sources": ["../../../src/core/prompts.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,IAAI,MAAM,WAAW,CAAC;AAC7B,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAEvE,MAAM,UAAU,mBAAmB,CAAC,UAAmB;IACrD,iFAAiF;IACjF,4FAA4F;IAC5F,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAI,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IAC7D,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,WAAW,EAAE,CAAC;IAChE,IAAI,WAAW,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACzD,eAAe,GAAG,IAAI,CAAC,CAAC,gCAAgC;QACxD,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACzC,YAAY,GAAG,WAAW,CAAC,CAAC,wCAAwC;QACtE,CAAC;QACD,iDAAiD;QACjD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,+BAA+B,YAAY,GAAG,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IACD,MAAM,UAAU,GAAG,eAAe;QAChC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;QACvC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;4FAmBsF,QAAQ,CAAC,IAAI,UAAU,QAAQ,CAAC,IAAI,wIAAwI,YAAY,CAAC,IAAI,UAAU,iBAAiB,CAAC,IAAI;;oDAErQ,QAAQ,CAAC,IAAI,OAAO,aAAa,CAAC,IAAI,MAAM,SAAS,CAAC,IAAI;;;;;;uOAMyH,aAAa,CAAC,IAAI,OAAO,QAAQ,CAAC,IAAI,UAAU,SAAS,CAAC,IAAI;;;;;;;;;;;;;8LAavG,SAAS,CAAC,IAAI;;;;;;;;;;;;;;;;mEAgBzI,SAAS,CAAC,IAAI;;;;uFAIM,YAAY,CAAC,IAAI,SAAS,aAAa,CAAC,IAAI;;oCAE/F,SAAS,CAAC,IAAI;;;oCAGd,UAAU,CAAC,IAAI;;;;;;;EAOjD,CAAC;YACD,0DAA0D;YAC1D,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,CAAC;YAC7D,MAAM,gBAAgB,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,iDAAiD;YAEjG,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO;;;CAGV,CAAC;YACA,CAAC;iBAAM,IAAI,gBAAgB,EAAE,CAAC;gBAC5B,OAAO;;;CAGV,CAAC;YACA,CAAC;iBAAM,CAAC;gBACN,OAAO;;;CAGV,CAAC;YACA,CAAC;QACH,CAAC,CAAC,EAAE;;EAEF,CAAC;YACD,IAAI,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;gBACnC,OAAO;;;;;;;;;;;;;;;CAeV,CAAC;YACA,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;qBAeiB,MAAM,CAAC,IAAI;;;;;qBAKX,SAAS,CAAC,IAAI;;;;;;;cAOrB,QAAQ,CAAC,IAAI;cACb,YAAY,CAAC,IAAI;;;;cAIjB,YAAY,CAAC,IAAI;;;;;;;;;;;;;cAajB,aAAa,CAAC,IAAI,OAAO,QAAQ,CAAC,IAAI;;cAEtC,SAAS,CAAC,IAAI;;;EAG1B,CAAC;YACD,IAAI,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;gBACnC,OAAO,uEAAuE,CAAC;YACjF,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,EAAE;;;;;;;;;;;;cAYU,YAAY,CAAC,IAAI,oDAAoD,QAAQ,CAAC,IAAI;;cAElF,iBAAiB,CAAC,IAAI;;cAEtB,aAAa,CAAC,IAAI;;cAElB,SAAS,CAAC,IAAI;;;;;;;cAOd,QAAQ,CAAC,IAAI;;;cAGb,YAAY,CAAC,IAAI;;;;;;;;cAQjB,QAAQ,CAAC,IAAI;;;;;;;;;4SASiR,YAAY,CAAC,IAAI,SAAS,iBAAiB,CAAC,IAAI;CAC3V,CAAC,IAAI,EAAE,CAAC;IAEP,uFAAuF;IACvF,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,WAAW,EAAE,CAAC;IAC3E,IAAI,gBAAgB,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;QACnE,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC7C,EAAE,CAAC,aAAa,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,8DAA8D;QAC5G,CAAC;aAAM,CAAC;YACN,EAAE,CAAC,aAAa,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC,CAAC,mDAAmD;QACrG,CAAC;IACH,CAAC;IAED,MAAM,YAAY,GAChB,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC;QACxC,CAAC,CAAC,cAAc,UAAU,CAAC,IAAI,EAAE,EAAE;QACnC,CAAC,CAAC,EAAE,CAAC;IAET,OAAO,GAAG,UAAU,GAAG,YAAY,EAAE,CAAC;AACxC,CAAC"}