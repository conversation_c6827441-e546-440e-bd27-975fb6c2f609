{"version": 3, "file": "modifiable-tool.test.js", "sourceRoot": "", "sources": ["../../../src/tools/modifiable-tool.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,EAAE,EACF,QAAQ,EACR,EAAE,EACF,MAAM,EACN,UAAU,EACV,SAAS,GAEV,MAAM,QAAQ,CAAC;AAChB,OAAO,EACL,gBAAgB,EAGhB,gBAAgB,GACjB,MAAM,sBAAsB,CAAC;AAE9B,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAE7B,oBAAoB;AACpB,MAAM,YAAY,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/C,MAAM,eAAe,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AAElD,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,CAAC;IACnC,QAAQ,EAAE,YAAY;CACvB,CAAC,CAAC,CAAC;AAEJ,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;IACrB,WAAW,EAAE,eAAe;CAC7B,CAAC,CAAC,CAAC;AAEJ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACd,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAQd,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,OAAe,CAAC;IACpB,IAAI,iBAA4C,CAAC;IACjD,IAAI,UAAsB,CAAC;IAC3B,IAAI,cAAsB,CAAC;IAC3B,IAAI,eAAuB,CAAC;IAC5B,IAAI,eAAuB,CAAC;IAC5B,IAAI,WAAwB,CAAC;IAE7B,UAAU,CAAC,GAAG,EAAE;QACd,EAAE,CAAC,aAAa,EAAE,CAAC;QAEnB,OAAO,GAAG,eAAe,CAAC;QAC1B,WAAW,GAAG,IAAI,eAAe,EAAE,CAAC,MAAM,CAAC;QAE3C,cAAc,GAAG,kCAAkC,CAAC;QACpD,eAAe,GAAG,kCAAkC,CAAC;QACrD,eAAe,GAAG,iDAAiD,CAAC;QACpE,UAAU,GAAG;YACX,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC;YACxC,cAAc,EAAE,OAAO;SACxB,CAAC;QAEF,iBAAiB,GAAG;YAClB,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC;YACzD,iBAAiB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,cAAc,CAAC;YAC5D,kBAAkB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,eAAe,CAAC;YAC9D,mBAAmB,EAAE,EAAE;iBACpB,EAAE,EAAE;iBACJ,kBAAkB,CAAC,CAAC,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;gBACpE,GAAG,cAAc;gBACjB,eAAe;gBACf,UAAU;aACX,CAAC,CAAC;SACN,CAAC;QAED,EAAE,CAAC,MAAe,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE5C,EAAE,CAAC,UAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC7C,EAAE,CAAC,SAAkB,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;QAC1D,EAAE,CAAC,aAAsB,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACvD,EAAE,CAAC,UAAmB,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAEpD,EAAE,CAAC,YAAqB,CAAC,kBAAkB,CAAC,CAAC,QAAgB,EAAE,EAAE;YAChE,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/B,OAAO,eAAe,CAAC;YACzB,CAAC;YACD,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,eAAe,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QACrD,YAAY,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,eAAe,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,UAAU,EACV,iBAAiB,EACjB,QAAsB,EACtB,WAAW,CACZ,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAC9D,UAAU,CACX,CAAC;YACF,MAAM,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAC/D,UAAU,CACX,CAAC;YACF,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YAEvE,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,uBAAuB,CAC9C,CAAC,EACD,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,8BAA8B,CAAC,CACnD,EACD,cAAc,EACd,MAAM,CACP,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,uBAAuB,CAC9C,CAAC,EACD,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,8BAA8B,CAAC,CACnD,EACD,eAAe,EACf,MAAM,CACP,CAAC;YAEF,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CACvC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAChC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAChC,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAC1C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAChC,MAAM,CACP,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAC1C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAChC,MAAM,CACP,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,CAChE,cAAc,EACd,eAAe,EACf,UAAU,CACX,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC1C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAClC,cAAc,EACd,eAAe,EACf,SAAS,EACT,UAAU,EACV,MAAM,CAAC,gBAAgB,CAAC;gBACtB,OAAO,EAAE,CAAC;gBACV,gBAAgB,EAAE,IAAI;aACvB,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,uBAAuB,CAC3C,CAAC,EACD,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CACjC,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,uBAAuB,CAC3C,CAAC,EACD,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CACjC,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,aAAa,EAAE;oBACb,GAAG,UAAU;oBACb,eAAe;oBACf,UAAU,EAAE,cAAc;iBAC3B;gBACD,WAAW,EAAE,mBAAmB;aACjC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YAChE,EAAE,CAAC,UAAmB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAE/C,MAAM,gBAAgB,CACpB,UAAU,EACV,iBAAiB,EACjB,QAAsB,EACtB,WAAW,CACZ,CAAC;YAEF,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACvC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,8BAA8B,CAAC,EAClD,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACpE,EAAE,CAAC,UAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAE9C,MAAM,gBAAgB,CACpB,UAAU,EACV,iBAAiB,EACjB,QAAsB,EACtB,WAAW,CACZ,CAAC;YAEF,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;QAC7D,EAAE,CAAC,YAAqB,CAAC,kBAAkB,CAAC,CAAC,QAAgB,EAAE,EAAE;YAChE,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;gBAC5D,KAA+B,CAAC,IAAI,GAAG,QAAQ,CAAC;gBACjD,MAAM,KAAK,CAAC;YACd,CAAC;YACD,OAAO,eAAe,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,UAAU,EACV,iBAAiB,EACjB,QAAsB,EACtB,WAAW,CACZ,CAAC;QAEF,MAAM,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC1C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAClC,EAAE,EACF,eAAe,EACf,SAAS,EACT,UAAU,EACV,MAAM,CAAC,gBAAgB,CAAC;YACtB,OAAO,EAAE,CAAC;YACV,gBAAgB,EAAE,IAAI;SACvB,CAAC,CACH,CAAC;QAEF,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;QAC7D,EAAE,CAAC,YAAqB,CAAC,kBAAkB,CAAC,CAAC,QAAgB,EAAE,EAAE;YAChE,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;gBAC5D,KAA+B,CAAC,IAAI,GAAG,QAAQ,CAAC;gBACjD,MAAM,KAAK,CAAC;YACd,CAAC;YACD,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,UAAU,EACV,iBAAiB,EACjB,QAAsB,EACtB,WAAW,CACZ,CAAC;QAEF,MAAM,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC1C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAClC,cAAc,EACd,EAAE,EACF,SAAS,EACT,UAAU,EACV,MAAM,CAAC,gBAAgB,CAAC;YACtB,OAAO,EAAE,CAAC;YACV,gBAAgB,EAAE,IAAI;SACvB,CAAC,CACH,CAAC;QAEF,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;QAC/D,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACvD,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAE5C,MAAM,MAAM,CACV,gBAAgB,CACd,UAAU,EACV,iBAAiB,EACjB,QAAsB,EACtB,WAAW,CACZ,CACF,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAE3C,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;QACjE,MAAM,eAAe,GAAG,EAAE;aACvB,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC;aACvB,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAC/B,EAAE,CAAC,UAAmB,CAAC,kBAAkB,CAAC,CAAC,SAAiB,EAAE,EAAE;YAC/D,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,MAAM,gBAAgB,CACpB,UAAU,EACV,iBAAiB,EACjB,QAAsB,EACtB,WAAW,CACZ,CAAC;QAEF,MAAM,CAAC,eAAe,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACjD,MAAM,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC1C,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAC1D,CAAC;QAEF,eAAe,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;QAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;QACtE,iBAAiB,CAAC,WAAW,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEtE,MAAM,gBAAgB,CACpB,UAAU,EACV,iBAAiB,EACjB,QAAsB,EACtB,WAAW,CACZ,CAAC;QAEF,MAAM,cAAc,GAAI,EAAE,CAAC,aAAsB,CAAC,IAAI,CAAC,KAAK,CAAC;QAC7D,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAEvC,MAAM,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QACzE,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QACzE,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,GAAG,OAAO,gCAAgC,CAAC,CAAC;QAC1E,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,GAAG,OAAO,gCAAgC,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;QACrE,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,WAAW;YACjB,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE;SACe,CAAC;QAE3C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;QACzE,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,WAAW;SACuB,CAAC;QAE3C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}