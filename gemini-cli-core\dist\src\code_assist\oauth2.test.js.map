{"version": 3, "file": "oauth2.test.js", "sourceRoot": "", "sources": ["../../../src/code_assist/oauth2.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACzE,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACnD,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAEzB,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;IACrC,MAAM,EAAE,GAAG,MAAM,cAAc,EAAuB,CAAC;IACvD,OAAO;QACL,GAAG,EAAE;QACL,OAAO,EAAE,EAAE,CAAC,EAAE,EAAE;KACjB,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC/B,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChB,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAElB,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,IAAI,WAAmB,CAAC;IAExB,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,EAAE,CAAC,WAAW,CAC1B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,uBAAuB,CAAC,CAChD,CAAC;QACF,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IACH,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QAC1C,MAAM,WAAW,GAAG,0BAA0B,CAAC;QAC/C,MAAM,QAAQ,GAAG,WAAW,CAAC;QAC7B,MAAM,SAAS,GAAG,YAAY,CAAC;QAC/B,MAAM,UAAU,GAAG;YACjB,YAAY,EAAE,mBAAmB;YACjC,aAAa,EAAE,oBAAoB;SACpC,CAAC;QAEF,MAAM,mBAAmB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;QACvE,MAAM,kBAAkB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,MAAM,gBAAgB,GAAG;YACvB,eAAe,EAAE,mBAAmB;YACpC,QAAQ,EAAE,YAAY;YACtB,cAAc,EAAE,kBAAkB;YAClC,WAAW,EAAE,UAAU;SACG,CAAC;QAC7B,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAEnE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,eAAe,CAAC,SAAkB,CAAC,CAAC;QACpE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE,CAAU,CAAC,CAAC;QAE9D,IAAI,eAGH,CAAC;QAEF,IAAI,uBAAiD,CAAC;QACtD,MAAM,sBAAsB,GAAG,IAAI,OAAO,CACxC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,uBAAuB,GAAG,OAAO,CAAC,CACjD,CAAC;QAEF,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,cAAc,GAAG;YACrB,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,IAAY,EAAE,QAAqB,EAAE,EAAE;gBACpD,YAAY,GAAG,IAAI,CAAC;gBACpB,IAAI,QAAQ,EAAE,CAAC;oBACb,QAAQ,EAAE,CAAC;gBACb,CAAC;gBACD,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC,CAAC;YACF,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,QAAqB,EAAE,EAAE;gBACrC,IAAI,QAAQ,EAAE,CAAC;oBACb,QAAQ,EAAE,CAAC;gBACb,CAAC;YACH,CAAC,CAAC;YACF,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;YACX,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;SACxC,CAAC;QACF,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE;YACrD,eAAe,GAAG,EAGjB,CAAC;YACF,OAAO,cAAwC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,cAAc,EAAE,CAAC;QAEvC,sCAAsC;QACtC,MAAM,sBAAsB,CAAC;QAE7B,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,wBAAwB,QAAQ,UAAU,SAAS,EAAE;SACnC,CAAC;QAC1B,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,EAAE,CAAC,EAAE,EAAE;YAClB,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;SACqB,CAAC;QAEpC,MAAM,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAExC,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC;QACnC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEtC,MAAM,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC/C,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC;YACxC,IAAI,EAAE,QAAQ;YACd,YAAY,EAAE,oBAAoB,YAAY,iBAAiB;SAChE,CAAC,CAAC;QACH,MAAM,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAE5D,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,kBAAkB,CAAC,CAAC;QACxE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;QAClE,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}