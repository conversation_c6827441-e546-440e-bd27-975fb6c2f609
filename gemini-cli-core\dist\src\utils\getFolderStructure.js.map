{"version": 3, "file": "getFolderStructure.js", "sourceRoot": "", "sources": ["../../../src/utils/getFolderStructure.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,MAAM,aAAa,CAAC;AAElC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAG3D,MAAM,SAAS,GAAG,GAAG,CAAC;AACtB,MAAM,oBAAoB,GAAG,KAAK,CAAC;AACnC,MAAM,uBAAuB,GAAG,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;AAuC1E,qBAAqB;AAErB,2BAA2B;AAE3B,KAAK,UAAU,iBAAiB,CAC9B,QAAgB,EAChB,OAAqC;IAErC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzC,MAAM,QAAQ,GAAmB;QAC/B,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,EAAE;QACT,UAAU,EAAE,EAAE;QACd,aAAa,EAAE,CAAC;QAChB,UAAU,EAAE,CAAC;KACd,CAAC;IAEF,MAAM,KAAK,GAA+D;QACxE,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE;KAChD,CAAC;IACF,IAAI,gBAAgB,GAAG,CAAC,CAAC;IACzB,gFAAgF;IAEhF,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC,CAAC,yDAAyD;IAEnG,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC,KAAK,EAAG,CAAC;QAEnD,IAAI,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,SAAS;QACX,CAAC;QACD,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAEhC,IAAI,gBAAgB,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACzC,yEAAyE;YACzE,qDAAqD;YACrD,6EAA6E;YAC7E,SAAS;QACX,CAAC;QAED,IAAI,OAAiB,CAAC;QACtB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1E,sEAAsE;YACtE,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IACE,WAAW,CAAC,KAAK,CAAC;gBAClB,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,EACpD,CAAC;gBACD,OAAO,CAAC,IAAI,CACV,qCAAqC,WAAW,KAAK,KAAK,CAAC,OAAO,EAAE,CACrE,CAAC;gBACF,IAAI,WAAW,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACxD,OAAO,IAAI,CAAC,CAAC,kCAAkC;gBACjD,CAAC;gBACD,6DAA6D;gBAC7D,SAAS;YACX,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;QAED,MAAM,iBAAiB,GAAa,EAAE,CAAC;QACvC,MAAM,sBAAsB,GAAqB,EAAE,CAAC;QAEpD,+CAA+C;QAC/C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBACnB,IAAI,gBAAgB,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACzC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC/B,MAAM;gBACR,CAAC;gBACD,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;gBAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAClD,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;oBACpD,IAAI,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACtD,SAAS;oBACX,CAAC;gBACH,CAAC;gBACD,IACE,CAAC,OAAO,CAAC,kBAAkB;oBAC3B,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,EACzC,CAAC;oBACD,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACjC,gBAAgB,EAAE,CAAC;oBACnB,UAAU,CAAC,UAAU,EAAE,CAAC;oBACxB,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QACD,UAAU,CAAC,KAAK,GAAG,iBAAiB,CAAC;QAErC,0CAA0C;QAC1C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxB,sEAAsE;gBACtE,qEAAqE;gBACrE,IAAI,gBAAgB,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACzC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBACpC,MAAM,CAAC,uDAAuD;gBAChE,CAAC;gBACD,oFAAoF;gBACpF,oFAAoF;gBACpF,wFAAwF;gBAExF,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC;gBACjC,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAE5D,IAAI,cAAc,GAAG,KAAK,CAAC;gBAC3B,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;oBACpD,IAAI,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;wBAC3D,cAAc,GAAG,IAAI,CAAC;oBACxB,CAAC;gBACH,CAAC;gBAED,IAAI,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,cAAc,EAAE,CAAC;oBAChE,MAAM,gBAAgB,GAAmB;wBACvC,IAAI,EAAE,aAAa;wBACnB,IAAI,EAAE,aAAa;wBACnB,KAAK,EAAE,EAAE;wBACT,UAAU,EAAE,EAAE;wBACd,aAAa,EAAE,CAAC;wBAChB,UAAU,EAAE,CAAC;wBACb,SAAS,EAAE,IAAI;qBAChB,CAAC;oBACF,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAC9C,gBAAgB,EAAE,CAAC,CAAC,kCAAkC;oBACtD,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,wCAAwC;oBACpE,SAAS;gBACX,CAAC;gBAED,MAAM,aAAa,GAAmB;oBACpC,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,EAAE;oBACT,UAAU,EAAE,EAAE;oBACd,aAAa,EAAE,CAAC;oBAChB,UAAU,EAAE,CAAC;iBACd,CAAC;gBACF,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC3C,gBAAgB,EAAE,CAAC;gBACnB,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,mCAAmC;gBAE/D,iDAAiD;gBACjD,KAAK,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QACD,UAAU,CAAC,UAAU,GAAG,sBAAsB,CAAC;IACjD,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;GAMG;AACH,SAAS,eAAe,CACtB,IAAoB,EACpB,aAAqB,EACrB,mBAA4B,EAC5B,oBAA6B,EAC7B,OAAiB;IAEjB,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;IAExD,kFAAkF;IAClF,0EAA0E;IAC1E,6DAA6D;IAC7D,mDAAmD;IACnD,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5C,OAAO,CAAC,IAAI,CACV,GAAG,aAAa,GAAG,SAAS,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,EAAE,CACzF,CAAC;IACJ,CAAC;IAED,wDAAwD;IACxD,iHAAiH;IACjH,uEAAuE;IACvE,MAAM,iBAAiB,GAAG,oBAAoB;QAC5C,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,aAAa,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAE5D,mCAAmC;IACnC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,MAAM,uBAAuB,GAC3B,CAAC,KAAK,SAAS,GAAG,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC;YAC5B,CAAC,IAAI,CAAC,iBAAiB,CAAC;QAC1B,MAAM,aAAa,GAAG,uBAAuB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QAChE,OAAO,CAAC,IAAI,CAAC,GAAG,iBAAiB,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvE,CAAC;IACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,MAAM,4BAA4B,GAChC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC;QAC1D,MAAM,aAAa,GAAG,4BAA4B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QACrE,OAAO,CAAC,IAAI,CAAC,GAAG,iBAAiB,GAAG,aAAa,GAAG,oBAAoB,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED,wCAAwC;IACxC,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;QACxC,MAAM,4BAA4B,GAChC,CAAC,KAAK,cAAc,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC;QACtD,8DAA8D;QAC9D,eAAe,CACb,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAClB,iBAAiB,EACjB,4BAA4B,EAC5B,KAAK,EACL,OAAO,CACR,CAAC;IACJ,CAAC;IACD,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,GAAG,iBAAiB,OAAO,oBAAoB,EAAE,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAED,iCAAiC;AAEjC;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,SAAiB,EACjB,OAAgC;IAEhC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC7C,MAAM,aAAa,GAAiC;QAClD,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,SAAS;QACxC,cAAc,EAAE,OAAO,EAAE,cAAc,IAAI,uBAAuB;QAClE,kBAAkB,EAAE,OAAO,EAAE,kBAAkB;QAC/C,WAAW,EAAE,OAAO,EAAE,WAAW;QACjC,gBAAgB,EAAE,OAAO,EAAE,gBAAgB,IAAI,IAAI;KACpD,CAAC;IAEF,IAAI,CAAC;QACH,uDAAuD;QACvD,MAAM,aAAa,GAAG,MAAM,iBAAiB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAE3E,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,oCAAoC,YAAY,gCAAgC,CAAC;QAC1F,CAAC;QAED,wCAAwC;QACxC,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,4CAA4C;QAC5C,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;QAE/D,mCAAmC;QACnC,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAErD,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,2EAA2E;QAC3E,8EAA8E;QAC9E,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAC/B,SAAS,kBAAkB,CAAC,IAAoB;YAC9C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAClE,kBAAkB,GAAG,IAAI,CAAC;YAC5B,CAAC;YACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACxB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClC,kBAAkB,CAAC,GAAG,CAAC,CAAC;oBACxB,IAAI,kBAAkB;wBAAE,MAAM;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;QACD,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAElC,IAAI,kBAAkB,EAAE,CAAC;YACvB,UAAU,GAAG,mCAAmC,oBAAoB,sEAAsE,aAAa,CAAC,QAAQ,sBAAsB,CAAC;QACzL,CAAC;QAED,MAAM,OAAO,GACX,iBAAiB,aAAa,CAAC,QAAQ,6BAA6B,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC;QAE1F,MAAM,MAAM,GAAG,GAAG,OAAO,OAAO,WAAW,MAAM,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7E,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,OAAO,CAAC,KAAK,CAAC,sCAAsC,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;QAC5E,OAAO,+BAA+B,YAAY,MAAM,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;IACnF,CAAC;AACH,CAAC"}