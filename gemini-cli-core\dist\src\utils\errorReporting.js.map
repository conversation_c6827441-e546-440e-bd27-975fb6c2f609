{"version": 3, "file": "errorReporting.js", "sourceRoot": "", "sources": ["../../../src/utils/errorReporting.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,kBAAkB,CAAC;AAClC,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,IAAI,MAAM,WAAW,CAAC;AAS7B;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAC/B,KAAsB,EACtB,WAAmB,EACnB,OAAyD,EACzD,IAAI,GAAG,SAAS;IAEhB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACjE,MAAM,cAAc,GAAG,uBAAuB,IAAI,IAAI,SAAS,OAAO,CAAC;IACvE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,cAAc,CAAC,CAAC;IAE1D,IAAI,aAAkD,CAAC;IACvD,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;QAC3B,aAAa,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;IACjE,CAAC;SAAM,IACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,SAAS,IAAI,KAAK,EAClB,CAAC;QACD,aAAa,GAAG;YACd,OAAO,EAAE,MAAM,CAAE,KAA8B,CAAC,OAAO,CAAC;SACzD,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,aAAa,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;IAC7C,CAAC;IAED,MAAM,aAAa,GAAoB,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;IAEhE,IAAI,OAAO,EAAE,CAAC;QACZ,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,CAAC;IAED,IAAI,wBAAgC,CAAC;IACrC,IAAI,CAAC;QACH,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACpE,CAAC;IAAC,OAAO,cAAc,EAAE,CAAC;QACxB,4DAA4D;QAC5D,OAAO,CAAC,KAAK,CACX,GAAG,WAAW,8DAA8D,EAC5E,cAAc,CACf,CAAC;QACF,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;QACzE,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,KAAK,CACX,kEAAkE,CACnE,CAAC;QACJ,CAAC;QACD,kEAAkE;QAClE,IAAI,CAAC;YACH,MAAM,oBAAoB,GAAG,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;YACtD,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACzE,wCAAwC;YACxC,MAAM,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC;YACzD,OAAO,CAAC,KAAK,CACX,GAAG,WAAW,qDAAqD,UAAU,EAAE,CAChF,CAAC;QACJ,CAAC;QAAC,OAAO,iBAAiB,EAAE,CAAC;YAC3B,OAAO,CAAC,KAAK,CACX,GAAG,WAAW,+CAA+C,EAC7D,iBAAiB,CAClB,CAAC;QACJ,CAAC;QACD,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,MAAM,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC;QACzD,OAAO,CAAC,KAAK,CAAC,GAAG,WAAW,8BAA8B,UAAU,EAAE,CAAC,CAAC;IAC1E,CAAC;IAAC,OAAO,UAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CACX,GAAG,WAAW,uDAAuD,EACrE,UAAU,CACX,CAAC;QACF,+DAA+D;QAC/D,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;QACzE,IAAI,OAAO,EAAE,CAAC;YACZ,0DAA0D;YAC1D,mFAAmF;YACnF,yGAAyG;YACzG,IAAI,CAAC;gBACH,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC;YAAC,MAAM,CAAC;gBACP,IAAI,CAAC;oBACH,OAAO,CAAC,KAAK,CACX,4CAA4C,EAC5C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAC3C,CAAC;gBACJ,CAAC;gBAAC,MAAM,CAAC;oBACP,OAAO,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;gBACxE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC"}