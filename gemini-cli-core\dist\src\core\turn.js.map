{"version": 3, "file": "turn.js", "sourceRoot": "", "sources": ["../../../src/core/turn.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAcH,OAAO,EAAE,eAAe,EAAE,MAAM,8CAA8C,CAAC;AAC/E,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzD,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAiBxE,MAAM,CAAN,IAAY,eAUX;AAVD,WAAY,eAAe;IACzB,sCAAmB,CAAA;IACnB,wDAAqC,CAAA;IACrC,0DAAuC,CAAA;IACvC,kEAA+C,CAAA;IAC/C,mDAAgC,CAAA;IAChC,kCAAe,CAAA;IACf,qDAAkC,CAAA;IAClC,mDAAgC,CAAA;IAChC,sCAAmB,CAAA;AACrB,CAAC,EAVW,eAAe,KAAf,eAAe,QAU1B;AAgGD,kEAAkE;AAClE,MAAM,OAAO,IAAI;IAKc;IAJpB,gBAAgB,CAAwB;IACzC,cAAc,CAA4B;IAC1C,iBAAiB,GAAgD,IAAI,CAAC;IAE9E,YAA6B,IAAgB;QAAhB,SAAI,GAAJ,IAAI,CAAY;QAC3C,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC3B,CAAC;IACD,iEAAiE;IACjE,KAAK,CAAC,CAAC,GAAG,CACR,GAAkB,EAClB,MAAmB;QAEnB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACvD,OAAO,EAAE,GAAG;gBACZ,MAAM,EAAE;oBACN,WAAW,EAAE,MAAM;iBACpB;aACF,CAAC,CAAC;YAEH,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;gBACxC,IAAI,MAAM,EAAE,OAAO,EAAE,CAAC;oBACpB,MAAM,EAAE,IAAI,EAAE,eAAe,CAAC,aAAa,EAAE,CAAC;oBAC9C,iEAAiE;oBACjE,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE/B,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC9D,IAAI,WAAW,EAAE,OAAO,EAAE,CAAC;oBACzB,wEAAwE;oBACxE,6EAA6E;oBAC7E,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC;oBACvC,MAAM,oBAAoB,GAAG,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;oBAC7D,MAAM,OAAO,GAAG,oBAAoB;wBAClC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;wBAChC,CAAC,CAAC,EAAE,CAAC;oBACP,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;oBACjE,MAAM,OAAO,GAAmB;wBAC9B,OAAO;wBACP,WAAW;qBACZ,CAAC;oBAEF,MAAM;wBACJ,IAAI,EAAE,eAAe,CAAC,OAAO;wBAC7B,KAAK,EAAE,OAAO;qBACf,CAAC;oBACF,SAAS;gBACX,CAAC;gBAED,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM,EAAE,IAAI,EAAE,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;gBACvD,CAAC;gBAED,oDAAoD;gBACpD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC;gBAC/C,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;oBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;oBACrD,IAAI,KAAK,EAAE,CAAC;wBACV,MAAM,KAAK,CAAC;oBACd,CAAC;gBACH,CAAC;gBAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,IAAI,CAAC,iBAAiB;wBACpB,IAAI,CAAC,aAAqD,CAAC;gBAC/D,CAAC;YACH,CAAC;YAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC1C,MAAM;oBACJ,IAAI,EAAE,eAAe,CAAC,aAAa;oBACnC,KAAK,EAAE,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,SAAS,EAAE,UAAU,EAAE;iBAC5D,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACjC,IAAI,KAAK,YAAY,iBAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,MAAM,EAAE,IAAI,EAAE,eAAe,CAAC,aAAa,EAAE,CAAC;gBAC9C,+CAA+C;gBAC/C,OAAO;YACT,CAAC;YAED,MAAM,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YAC1E,MAAM,WAAW,CACf,KAAK,EACL,kCAAkC,EAClC,gBAAgB,EAChB,4BAA4B,CAC7B,CAAC;YACF,MAAM,MAAM,GACV,OAAO,KAAK,KAAK,QAAQ;gBACzB,KAAK,KAAK,IAAI;gBACd,QAAQ,IAAI,KAAK;gBACjB,OAAQ,KAA6B,CAAC,MAAM,KAAK,QAAQ;gBACvD,CAAC,CAAE,KAA4B,CAAC,MAAM;gBACtC,CAAC,CAAC,SAAS,CAAC;YAChB,MAAM,eAAe,GAAoB;gBACvC,OAAO,EAAE,eAAe,CAAC,KAAK,CAAC;gBAC/B,MAAM;aACP,CAAC;YACF,MAAM,EAAE,IAAI,EAAE,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,CAAC;YACzE,OAAO;QACT,CAAC;IACH,CAAC;IAEO,yBAAyB,CAC/B,MAAoB;QAEpB,MAAM,MAAM,GACV,MAAM,CAAC,EAAE;YACT,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QACxE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,qBAAqB,CAAC;QAClD,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAA4B,CAAC;QAE5D,MAAM,eAAe,GAAwB;YAC3C,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,iBAAiB,EAAE,KAAK;SACzB,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE5C,uEAAuE;QACvE,OAAO,EAAE,IAAI,EAAE,eAAe,CAAC,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC;IAC3E,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAChC,CAAC;CACF"}