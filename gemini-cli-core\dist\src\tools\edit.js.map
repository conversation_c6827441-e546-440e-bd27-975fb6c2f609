{"version": 3, "file": "edit.js", "sourceRoot": "", "sources": ["../../../src/tools/edit.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EACL,QAAQ,EAER,uBAAuB,GAIxB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAEjD,OAAO,EAAU,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAqC9C;;GAEG;AACH,MAAM,OAAO,QACX,SAAQ,QAAoC;IAG5C,MAAM,CAAU,IAAI,GAAG,SAAS,CAAC;IAChB,MAAM,CAAS;IACf,aAAa,CAAS;IACtB,MAAM,CAAe;IAEtC;;;OAGG;IACH,YAAY,MAAc;QACxB,KAAK,CACH,QAAQ,CAAC,IAAI,EACb,MAAM,EACN,2QAA2Q,YAAY,CAAC,IAAI;;;;;;;;gPAQlD,EAC1O;YACE,UAAU,EAAE;gBACV,SAAS,EAAE;oBACT,WAAW,EACT,+DAA+D;oBACjE,IAAI,EAAE,QAAQ;iBACf;gBACD,UAAU,EAAE;oBACV,WAAW,EACT,wYAAwY;oBAC1Y,IAAI,EAAE,QAAQ;iBACf;gBACD,UAAU,EAAE;oBACV,WAAW,EACT,wJAAwJ;oBAC1J,IAAI,EAAE,QAAQ;iBACf;gBACD,qBAAqB,EAAE;oBACrB,IAAI,EAAE,QAAQ;oBACd,WAAW,EACT,qHAAqH;oBACvH,OAAO,EAAE,CAAC;iBACX;aACF;YACD,QAAQ,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC;YACnD,IAAI,EAAE,QAAQ;SACf,CACF,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACK,YAAY,CAAC,WAAmB;QACtC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACnD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;QAC1C,MAAM,WAAW,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;YACnD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC;QAC9B,OAAO,CACL,cAAc,KAAK,cAAc;YACjC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,CACvC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,kBAAkB,CAAC,MAAsB;QACvC,IACE,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,CAAC,eAAe,CAAC,QAAQ,CACvB,IAAI,CAAC,MAAM,CAAC,UAAqC,EACjD,MAAM,CACP,EACD,CAAC;YACD,OAAO,sCAAsC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,OAAO,+BAA+B,MAAM,CAAC,SAAS,EAAE,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YACzC,OAAO,gDAAgD,IAAI,CAAC,aAAa,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;QACpG,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,iBAAiB,CACvB,cAA6B,EAC7B,SAAiB,EACjB,SAAiB,EACjB,SAAkB;QAElB,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;YAC5B,4GAA4G;YAC5G,OAAO,SAAS,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,CAAC;QACD,4EAA4E;QAC5E,IAAI,SAAS,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;YACnC,OAAO,cAAc,CAAC;QACxB,CAAC;QACD,OAAO,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACzD,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,aAAa,CACzB,MAAsB,EACtB,WAAwB;QAExB,MAAM,oBAAoB,GAAG,MAAM,CAAC,qBAAqB,IAAI,CAAC,CAAC;QAC/D,IAAI,cAAc,GAAkB,IAAI,CAAC;QACzC,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC;QACvC,IAAI,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC;QACvC,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,KAAK,GAAiD,SAAS,CAAC;QAEpE,IAAI,CAAC;YACH,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAC3D,0DAA0D;YAC1D,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACvD,UAAU,GAAG,IAAI,CAAC;QACpB,CAAC;QAAC,OAAO,GAAY,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC/C,mDAAmD;gBACnD,MAAM,GAAG,CAAC;YACZ,CAAC;YACD,UAAU,GAAG,KAAK,CAAC;QACrB,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;YAC5C,sBAAsB;YACtB,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC;aAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACvB,mEAAmE;YACnE,KAAK,GAAG;gBACN,OAAO,EAAE,kFAAkF;gBAC3F,GAAG,EAAE,mBAAmB,MAAM,CAAC,SAAS,EAAE;aAC3C,CAAC;QACJ,CAAC;aAAM,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;YACnC,2BAA2B;YAC3B,MAAM,aAAa,GAAG,MAAM,iBAAiB,CAC3C,cAAc,EACd,MAAM,EACN,IAAI,CAAC,MAAM,EACX,WAAW,CACZ,CAAC;YACF,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC;YACjD,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC;YACjD,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;YAExC,IAAI,MAAM,CAAC,UAAU,KAAK,EAAE,EAAE,CAAC;gBAC7B,qDAAqD;gBACrD,KAAK,GAAG;oBACN,OAAO,EAAE,iEAAiE;oBAC1E,GAAG,EAAE,uCAAuC,MAAM,CAAC,SAAS,EAAE;iBAC/D,CAAC;YACJ,CAAC;iBAAM,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;gBAC7B,KAAK,GAAG;oBACN,OAAO,EAAE,uDAAuD;oBAChE,GAAG,EAAE,yDAAyD,MAAM,CAAC,SAAS,mKAAmK,YAAY,CAAC,IAAI,kBAAkB;iBACrR,CAAC;YACJ,CAAC;iBAAM,IAAI,WAAW,KAAK,oBAAoB,EAAE,CAAC;gBAChD,KAAK,GAAG;oBACN,OAAO,EAAE,4BAA4B,oBAAoB,4BAA4B,WAAW,GAAG;oBACnG,GAAG,EAAE,4BAA4B,oBAAoB,0BAA0B,WAAW,4BAA4B,MAAM,CAAC,SAAS,EAAE;iBACzI,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,gFAAgF;YAChF,KAAK,GAAG;gBACN,OAAO,EAAE,iCAAiC;gBAC1C,GAAG,EAAE,4CAA4C,MAAM,CAAC,SAAS,EAAE;aACpE,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CACvC,cAAc,EACd,cAAc,EACd,cAAc,EACd,SAAS,CACV,CAAC;QAEF,OAAO;YACL,cAAc;YACd,UAAU;YACV,WAAW;YACX,KAAK;YACL,SAAS;SACV,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,oBAAoB,CACxB,MAAsB,EACtB,WAAwB;QAExB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CACX,sEAAsE,eAAe,EAAE,CACxF,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAwB,CAAC;QAC7B,IAAI,CAAC;YACH,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACxE,OAAO,CAAC,GAAG,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;YACjD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,UAAU,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAC/B,QAAQ,EACR,QAAQ,CAAC,cAAc,IAAI,EAAE,EAC7B,QAAQ,CAAC,UAAU,EACnB,SAAS,EACT,UAAU,EACV,oBAAoB,CACrB,CAAC;QACF,MAAM,mBAAmB,GAAgC;YACvD,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,iBAAiB,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE;YACzF,QAAQ;YACR,QAAQ;YACR,SAAS,EAAE,KAAK,EAAE,OAAgC,EAAE,EAAE;gBACpD,IAAI,OAAO,KAAK,uBAAuB,CAAC,aAAa,EAAE,CAAC;oBACtD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;SACF,CAAC;QACF,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,cAAc,CAAC,MAAsB;QACnC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAClE,OAAO,sDAAsD,CAAC;QAChE,CAAC;QACD,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACxE,IAAI,MAAM,CAAC,UAAU,KAAK,EAAE,EAAE,CAAC;YAC7B,OAAO,UAAU,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC;QAC/C,CAAC;QAED,MAAM,gBAAgB,GACpB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YACjD,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/C,MAAM,gBAAgB,GACpB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YACjD,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE/C,IAAI,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC,UAAU,EAAE,CAAC;YAC5C,OAAO,sBAAsB,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC;QAC3D,CAAC;QACD,OAAO,GAAG,WAAW,CAAC,YAAY,CAAC,KAAK,gBAAgB,OAAO,gBAAgB,EAAE,CAAC;IACpF,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO,CACX,MAAsB,EACtB,MAAmB;QAEnB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE,+CAA+C,eAAe,EAAE;gBAC5E,aAAa,EAAE,UAAU,eAAe,EAAE;aAC3C,CAAC;QACJ,CAAC;QAED,IAAI,QAAwB,CAAC;QAC7B,IAAI,CAAC;YACH,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACxE,OAAO;gBACL,UAAU,EAAE,yBAAyB,QAAQ,EAAE;gBAC/C,aAAa,EAAE,yBAAyB,QAAQ,EAAE;aACnD,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnB,OAAO;gBACL,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG;gBAC9B,aAAa,EAAE,UAAU,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE;aAClD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACpD,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAEhE,IAAI,aAAgC,CAAC;YACrC,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACvB,aAAa,GAAG,WAAW,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;YAC/F,CAAC;iBAAM,CAAC;gBACN,gFAAgF;gBAChF,uDAAuD;gBACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAC/B,QAAQ,EACR,QAAQ,CAAC,cAAc,IAAI,EAAE,EAAE,2CAA2C;gBAC1E,QAAQ,CAAC,UAAU,EACnB,SAAS,EACT,UAAU,EACV,oBAAoB,CACrB,CAAC;gBACF,aAAa,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;YACzC,CAAC;YAED,MAAM,iBAAiB,GAAG,QAAQ,CAAC,SAAS;gBAC1C,CAAC,CAAC,qBAAqB,MAAM,CAAC,SAAS,yBAAyB;gBAChE,CAAC,CAAC,+BAA+B,MAAM,CAAC,SAAS,KAAK,QAAQ,CAAC,WAAW,iBAAiB,CAAC;YAE9F,OAAO;gBACL,UAAU,EAAE,iBAAiB;gBAC7B,aAAa,EAAE,aAAa;aAC7B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACxE,OAAO;gBACL,UAAU,EAAE,yBAAyB,QAAQ,EAAE;gBAC/C,aAAa,EAAE,uBAAuB,QAAQ,EAAE;aACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,QAAgB;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED,gBAAgB,CAAC,CAAc;QAC7B,OAAO;YACL,WAAW,EAAE,CAAC,MAAsB,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS;YACzD,iBAAiB,EAAE,KAAK,EAAE,MAAsB,EAAmB,EAAE;gBACnE,IAAI,CAAC;oBACH,OAAO,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACnD,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;wBAAE,MAAM,GAAG,CAAC;oBAC1D,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC;YACD,kBAAkB,EAAE,KAAK,EAAE,MAAsB,EAAmB,EAAE;gBACpE,IAAI,CAAC;oBACH,MAAM,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;oBACjE,OAAO,IAAI,CAAC,iBAAiB,CAC3B,cAAc,EACd,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,UAAU,KAAK,EAAE,IAAI,cAAc,KAAK,EAAE,CAClD,CAAC;gBACJ,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;wBAAE,MAAM,GAAG,CAAC;oBAC1D,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC;YACD,mBAAmB,EAAE,CACnB,UAAkB,EAClB,uBAA+B,EAC/B,cAA8B,EACd,EAAE,CAAC,CAAC;gBACpB,GAAG,cAAc;gBACjB,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,uBAAuB;aACpC,CAAC;SACH,CAAC;IACJ,CAAC"}