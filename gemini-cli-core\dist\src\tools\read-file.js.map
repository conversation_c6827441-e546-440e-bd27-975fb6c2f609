{"version": 3, "file": "read-file.js", "sourceRoot": "", "sources": ["../../../src/tools/read-file.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,QAAQ,EAAc,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,wBAAwB,EAAE,MAAM,uBAAuB,CAAC;AAE/E,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EACL,yBAAyB,EACzB,aAAa,GACd,MAAM,yBAAyB,CAAC;AAsBjC;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,QAAwC;IAI9D;IACA;IAJV,MAAM,CAAU,IAAI,GAAW,WAAW,CAAC;IAE3C,YACU,aAAqB,EACrB,MAAc;QAEtB,KAAK,CACH,YAAY,CAAC,IAAI,EACjB,UAAU,EACV,qMAAqM,EACrM;YACE,UAAU,EAAE;gBACV,aAAa,EAAE;oBACb,WAAW,EACT,mJAAmJ;oBACrJ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,IAAI;iBACd;gBACD,MAAM,EAAE;oBACN,WAAW,EACT,8IAA8I;oBAChJ,IAAI,EAAE,QAAQ;iBACf;gBACD,KAAK,EAAE;oBACL,WAAW,EACT,uLAAuL;oBACzL,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE,CAAC,eAAe,CAAC;YAC3B,IAAI,EAAE,QAAQ;SACf,CACF,CAAC;QA7BM,kBAAa,GAAb,aAAa,CAAQ;QACrB,WAAM,GAAN,MAAM,CAAQ;QA6BtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IACnD,CAAC;IAED,kBAAkB,CAAC,MAA0B;QAC3C,IACE,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,CAAC,eAAe,CAAC,QAAQ,CACvB,IAAI,CAAC,MAAM,CAAC,UAAqC,EACjD,MAAM,CACP,EACD,CAAC;YACD,OAAO,sCAAsC,CAAC;QAChD,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,OAAO,iDAAiD,QAAQ,sCAAsC,CAAC;QACzG,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YAChD,OAAO,gDAAgD,IAAI,CAAC,aAAa,MAAM,QAAQ,EAAE,CAAC;QAC5F,CAAC;QACD,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrD,OAAO,sCAAsC,CAAC;QAChD,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC;YACpD,OAAO,iCAAiC,CAAC;QAC3C,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QACjD,IAAI,WAAW,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YAC7D,MAAM,YAAY,GAAG,YAAY,CAC/B,MAAM,CAAC,aAAa,EACpB,IAAI,CAAC,aAAa,CACnB,CAAC;YACF,OAAO,cAAc,WAAW,CAAC,YAAY,CAAC,2CAA2C,CAAC;QAC5F,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,MAA0B;QACvC,IACE,CAAC,MAAM;YACP,OAAO,MAAM,CAAC,aAAa,KAAK,QAAQ;YACxC,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,EAClC,CAAC;YACD,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QACD,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5E,OAAO,WAAW,CAAC,YAAY,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAA0B,EAC1B,OAAoB;QAEpB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE,+CAA+C,eAAe,EAAE;gBAC5E,aAAa,EAAE,eAAe;aAC/B,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,wBAAwB,CAC3C,MAAM,CAAC,aAAa,EACpB,IAAI,CAAC,aAAa,EAClB,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,KAAK,CACb,CAAC;QAEF,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,OAAO;gBACL,UAAU,EAAE,MAAM,CAAC,KAAK,EAAE,6BAA6B;gBACvD,aAAa,EAAE,MAAM,CAAC,aAAa,EAAE,sBAAsB;aAC5D,CAAC;QACJ,CAAC;QAED,MAAM,KAAK,GACT,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ;YACnC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;YACtC,CAAC,CAAC,SAAS,CAAC;QAChB,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC3D,yBAAyB,CACvB,IAAI,CAAC,MAAM,EACX,aAAa,CAAC,IAAI,EAClB,KAAK,EACL,QAAQ,EACR,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CACnC,CAAC;QAEF,OAAO;YACL,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;SACpC,CAAC;IACJ,CAAC"}