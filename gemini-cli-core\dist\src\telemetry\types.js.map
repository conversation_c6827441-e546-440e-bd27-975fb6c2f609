{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/telemetry/types.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAKH,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,QAAQ,EAAE,MAAM,6BAA6B,CAAC;AAEvD,MAAM,CAAN,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IAC1B,qCAAiB,CAAA;IACjB,qCAAiB,CAAA;IACjB,qCAAiB,CAAA;AACnB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,QAI3B;AAED,MAAM,UAAU,sBAAsB,CACpC,OAAgC;IAEhC,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,uBAAuB,CAAC,WAAW,CAAC;QACzC,KAAK,uBAAuB,CAAC,aAAa,CAAC;QAC3C,KAAK,uBAAuB,CAAC,mBAAmB,CAAC;QACjD,KAAK,uBAAuB,CAAC,iBAAiB;YAC5C,OAAO,gBAAgB,CAAC,MAAM,CAAC;QACjC,KAAK,uBAAuB,CAAC,gBAAgB;YAC3C,OAAO,gBAAgB,CAAC,MAAM,CAAC;QACjC,KAAK,uBAAuB,CAAC,MAAM,CAAC;QACpC;YACE,OAAO,gBAAgB,CAAC,MAAM,CAAC;IACnC,CAAC;AACH,CAAC;AAED,MAAM,OAAO,iBAAiB;IAC5B,YAAY,CAAe;IAC3B,iBAAiB,CAAS,CAAC,WAAW;IACtC,KAAK,CAAS;IACd,eAAe,CAAS;IACxB,eAAe,CAAU;IACzB,kBAAkB,CAAS;IAC3B,aAAa,CAAS;IACtB,eAAe,CAAU;IACzB,iBAAiB,CAAU;IAC3B,aAAa,CAAU;IACvB,WAAW,CAAS;IACpB,iBAAiB,CAAU;IAC3B,kCAAkC,CAAU;IAC5C,iCAAiC,CAAU;IAE3C,YAAY,MAAc;QACxB,MAAM,eAAe,GAAG,MAAM,CAAC,yBAAyB,EAAE,CAAC;QAC3D,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAE1C,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,eAAe,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC;YAChD,SAAS,GAAG,eAAe,CAAC,QAAQ,KAAK,QAAQ,CAAC,UAAU,CAAC;YAC7D,SAAS,GAAG,eAAe,CAAC,QAAQ,KAAK,QAAQ,CAAC,aAAa,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAClD,IAAI,CAAC,eAAe;YAClB,OAAO,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;QACnE,IAAI,CAAC,kBAAkB,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClE,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;QAC9C,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,SAAS,CAAC;QAC9C,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvE,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;QACtD,IAAI,CAAC,kCAAkC;YACrC,MAAM,CAAC,6BAA6B,EAAE,CAAC;QACzC,IAAI,CAAC,iCAAiC;YACpC,MAAM,CAAC,gCAAgC,EAAE,CAAC;IAC9C,CAAC;CACF;AAED,MAAM,OAAO,eAAe;IAC1B,YAAY,CAAgB;IAC5B,iBAAiB,CAAS,CAAC,WAAW;IACtC,UAAU,CAAU;IAEpB,YAAY,MAAe;QACzB,IAAI,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,UAAU,GAAG,MAAM,EAAE,YAAY,EAAE,CAAC;IAC3C,CAAC;CACF;AAED,MAAM,OAAO,eAAe;IAC1B,YAAY,CAAgB;IAC5B,iBAAiB,CAAS,CAAC,WAAW;IACtC,aAAa,CAAS;IACtB,MAAM,CAAU;IAEhB,YAAY,aAAqB,EAAE,MAAe;QAChD,IAAI,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;CACF;AAED,MAAM,OAAO,aAAa;IACxB,YAAY,CAAc;IAC1B,iBAAiB,CAAS,CAAC,WAAW;IACtC,aAAa,CAAS;IACtB,aAAa,CAA0B;IACvC,WAAW,CAAS;IACpB,OAAO,CAAU;IACjB,QAAQ,CAAoB;IAC5B,KAAK,CAAU;IACf,UAAU,CAAU;IAEpB,YAAY,IAAuB;QACjC,IAAI,CAAC,YAAY,CAAC,GAAG,WAAW,CAAC;QACjC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO;YAC1B,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC;YACtC,CAAC,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC;QAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC;IAC9C,CAAC;CACF;AAED,MAAM,OAAO,eAAe;IAC1B,YAAY,CAAgB;IAC5B,iBAAiB,CAAS,CAAC,WAAW;IACtC,KAAK,CAAS;IACd,YAAY,CAAU;IAEtB,YAAY,KAAa,EAAE,YAAqB;QAC9C,IAAI,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;CACF;AAED,MAAM,OAAO,aAAa;IACxB,YAAY,CAAc;IAC1B,iBAAiB,CAAS,CAAC,WAAW;IACtC,KAAK,CAAS;IACd,KAAK,CAAS;IACd,UAAU,CAAU;IACpB,WAAW,CAAmB;IAC9B,WAAW,CAAS;IAEpB,YACE,KAAa,EACb,KAAa,EACb,WAAmB,EACnB,UAAmB,EACnB,WAA6B;QAE7B,IAAI,CAAC,YAAY,CAAC,GAAG,WAAW,CAAC;QACjC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;CACF;AAED,MAAM,OAAO,gBAAgB;IAC3B,YAAY,CAAiB;IAC7B,iBAAiB,CAAS,CAAC,WAAW;IACtC,KAAK,CAAS;IACd,WAAW,CAAmB;IAC9B,WAAW,CAAS;IACpB,KAAK,CAAU;IACf,iBAAiB,CAAS;IAC1B,kBAAkB,CAAS;IAC3B,0BAA0B,CAAS;IACnC,oBAAoB,CAAS;IAC7B,gBAAgB,CAAS;IACzB,aAAa,CAAU;IAEvB,YACE,KAAa,EACb,WAAmB,EACnB,UAAiD,EACjD,aAAsB,EACtB,KAAc;QAEd,IAAI,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;QACpC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;QACvB,IAAI,CAAC,iBAAiB,GAAG,UAAU,EAAE,gBAAgB,IAAI,CAAC,CAAC;QAC3D,IAAI,CAAC,kBAAkB,GAAG,UAAU,EAAE,oBAAoB,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,0BAA0B,GAAG,UAAU,EAAE,uBAAuB,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,oBAAoB,GAAG,UAAU,EAAE,kBAAkB,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,gBAAgB,GAAG,UAAU,EAAE,uBAAuB,IAAI,CAAC,CAAC;QACjE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;CACF"}