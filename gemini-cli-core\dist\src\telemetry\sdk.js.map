{"version": 3, "file": "sdk.js", "sourceRoot": "", "sources": ["../../../src/telemetry/sdk.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC3E,OAAO,EAAE,iBAAiB,EAAE,MAAM,yCAAyC,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAC;AACzE,OAAO,EAAE,kBAAkB,EAAE,MAAM,2CAA2C,CAAC;AAC/E,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,0BAA0B,EAAE,MAAM,qCAAqC,CAAC;AACjF,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EACL,kBAAkB,EAClB,mBAAmB,GACpB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EACL,uBAAuB,EACvB,wBAAwB,GACzB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,qBAAqB,EACrB,6BAA6B,GAC9B,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAE1E,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AACjD,OAAO,EAAE,cAAc,EAAE,MAAM,sCAAsC,CAAC;AAEtE,+DAA+D;AAC/D,IAAI,CAAC,SAAS,CAAC,IAAI,iBAAiB,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;AAE3D,IAAI,GAAwB,CAAC;AAC7B,IAAI,oBAAoB,GAAG,KAAK,CAAC;AAEjC,MAAM,UAAU,yBAAyB;IACvC,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED,SAAS,iBAAiB,CACxB,mBAAuC;IAEvC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACzB,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,kEAAkE;IAClE,MAAM,eAAe,GAAG,mBAAmB,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAExE,IAAI,CAAC;QACH,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC;QACrC,0EAA0E;QAC1E,2EAA2E;QAC3E,OAAO,GAAG,CAAC,MAAM,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;QAC1E,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,MAAc;IAChD,IAAI,oBAAoB,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,EAAE,CAAC;QAC1D,OAAO;IACT,CAAC;IAED,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC;QAC5B,CAAC,0BAA0B,CAAC,YAAY,CAAC,EAAE,YAAY;QACvD,CAAC,0BAA0B,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,OAAO;QAC7D,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE;KACpC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,MAAM,CAAC,wBAAwB,EAAE,CAAC;IACvD,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC3D,MAAM,OAAO,GAAG,CAAC,CAAC,kBAAkB,CAAC;IAErC,MAAM,YAAY,GAAG,OAAO;QAC1B,CAAC,CAAC,IAAI,iBAAiB,CAAC;YACpB,GAAG,EAAE,kBAAkB;YACvB,WAAW,EAAE,oBAAoB,CAAC,IAAI;SACvC,CAAC;QACJ,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;IAC9B,MAAM,WAAW,GAAG,OAAO;QACzB,CAAC,CAAC,IAAI,eAAe,CAAC;YAClB,GAAG,EAAE,kBAAkB;YACvB,WAAW,EAAE,oBAAoB,CAAC,IAAI;SACvC,CAAC;QACJ,CAAC,CAAC,IAAI,wBAAwB,EAAE,CAAC;IACnC,MAAM,YAAY,GAAG,OAAO;QAC1B,CAAC,CAAC,IAAI,6BAA6B,CAAC;YAChC,QAAQ,EAAE,IAAI,kBAAkB,CAAC;gBAC/B,GAAG,EAAE,kBAAkB;gBACvB,WAAW,EAAE,oBAAoB,CAAC,IAAI;aACvC,CAAC;YACF,oBAAoB,EAAE,KAAK;SAC5B,CAAC;QACJ,CAAC,CAAC,IAAI,6BAA6B,CAAC;YAChC,QAAQ,EAAE,IAAI,qBAAqB,EAAE;YACrC,oBAAoB,EAAE,KAAK;SAC5B,CAAC,CAAC;IAEP,GAAG,GAAG,IAAI,OAAO,CAAC;QAChB,QAAQ;QACR,cAAc,EAAE,CAAC,IAAI,kBAAkB,CAAC,YAAY,CAAC,CAAC;QACtD,kBAAkB,EAAE,IAAI,uBAAuB,CAAC,WAAW,CAAC;QAC5D,YAAY;QACZ,gBAAgB,EAAE,CAAC,IAAI,mBAAmB,EAAE,CAAC;KAC9C,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,GAAG,CAAC,KAAK,EAAE,CAAC;QACZ,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,oBAAoB,GAAG,IAAI,CAAC;QAC5B,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IACzC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;AAC1C,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,iBAAiB;IACrC,IAAI,CAAC,oBAAoB,IAAI,CAAC,GAAG,EAAE,CAAC;QAClC,OAAO;IACT,CAAC;IACD,IAAI,CAAC;QACH,cAAc,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,CAAC;QACzC,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAC3D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;YAAS,CAAC;QACT,oBAAoB,GAAG,KAAK,CAAC;IAC/B,CAAC;AACH,CAAC"}